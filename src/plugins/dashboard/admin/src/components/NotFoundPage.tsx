import { useNavigate } from 'react-router-dom';

const NotFoundPage = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate('/plugins/dashboard');
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '60vh',
      backgroundColor: '#212134',
      borderRadius: '8px',
      padding: '40px',
      textAlign: 'center',
      margin: '20px'
    }}>
      {/* 404 Icon */}
      <div style={{
        fontSize: '72px',
        marginBottom: '24px'
      }}>
        🔍
      </div>

      {/* Error Code */}
      <h1 style={{
        fontSize: '48px',
        fontWeight: 'bold',
        color: '#d32f2f',
        margin: '0 0 16px 0'
      }}>
        404
      </h1>

      {/* Error Message */}
      <h2 style={{
        fontSize: '24px',
        fontWeight: '600',
        color: 'white',
        margin: '0 0 16px 0'
      }}>
        Page Not Found
      </h2>

      <p style={{
        fontSize: '16px',
        color: '#a5a5ba',
        marginBottom: '32px',
        maxWidth: '500px',
        lineHeight: '1.5'
      }}>
        The page you're looking for doesn't exist or has been moved. 
        Please check the URL or return to the dashboard.
      </p>

      {/* Action Buttons */}
      <div style={{
        display: 'flex',
        gap: '16px',
        flexWrap: 'wrap',
        justifyContent: 'center'
      }}>
        <button
          onClick={handleGoBack}
          style={{
            padding: '12px 24px',
            backgroundColor: '#4945ff',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            fontSize: '16px',
            fontWeight: '500',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            transition: 'background-color 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#3832e1';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#4945ff';
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 19L5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          Back to Dashboard
        </button>

        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '12px 24px',
            backgroundColor: 'transparent',
            color: '#a5a5ba',
            border: '1px solid #32324d',
            borderRadius: '6px',
            fontSize: '16px',
            fontWeight: '500',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#32324d';
            e.currentTarget.style.color = 'white';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = '#a5a5ba';
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M1 4V10H7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M23 20V14H17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          Refresh Page
        </button>
      </div>

      {/* Additional Help */}
      <div style={{
        marginTop: '40px',
        padding: '20px',
        backgroundColor: '#181826',
        borderRadius: '6px',
        border: '1px solid #32324d',
        maxWidth: '500px'
      }}>
        <h3 style={{
          fontSize: '16px',
          fontWeight: '600',
          color: 'white',
          margin: '0 0 12px 0'
        }}>
          Need Help?
        </h3>
        <p style={{
          fontSize: '14px',
          color: '#a5a5ba',
          margin: '0 0 16px 0',
          lineHeight: '1.4'
        }}>
          If you believe this is an error, please contact your administrator or check the following:
        </p>
        <ul style={{
          fontSize: '14px',
          color: '#a5a5ba',
          margin: 0,
          paddingLeft: '20px',
          lineHeight: '1.6'
        }}>
          <li>Verify the URL is correct</li>
          <li>Check if you have proper permissions</li>
          <li>Try refreshing the page</li>
        </ul>
      </div>
    </div>
  );
};

export default NotFoundPage;
