import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>graphy,
  Button,
  Flex,
  Grid,
  Card,
  CardBody,
  TextInput,
  Textarea,
  NumberInput,
  Select,
  Option,
  Field,
  FieldLabel,
  FieldInput,
  FieldError,
  Alert
} from '@strapi/design-system';
import { Check } from '@strapi/icons';

interface CollectionFormProps {
  config: any;
  mode: 'create' | 'edit';
  initialData?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
}

const CollectionForm = ({ 
  config, 
  mode, 
  initialData, 
  onSubmit, 
  onCancel 
}: CollectionFormProps) => {
  const [formData, setFormData] = useState<any>({});
  const [errors, setErrors] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  useEffect(() => {
    if (mode === 'edit' && initialData) {
      const data: any = {};
      config.fields.forEach((field: any) => {
        if (field.type === 'relation' && initialData[field.key]) {
          data[field.key] = initialData[field.key].id;
        } else {
          data[field.key] = initialData[field.key] || '';
        }
      });
      setFormData(data);
    } else {
      const data: any = {};
      config.fields.forEach((field: any) => {
        data[field.key] = field.type === 'number' ? 0 : '';
      });
      setFormData(data);
    }
  }, [mode, initialData, config.fields]);

  const handleInputChange = (key: string, value: any) => {
    setFormData((prev: any) => ({ ...prev, [key]: value }));
    if (errors[key]) {
      setErrors((prev: any) => ({ ...prev, [key]: null }));
    }
  };

  const validateForm = () => {
    const newErrors: any = {};
    
    config.fields.forEach((field: any) => {
      if (field.required && (!formData[field.key] || formData[field.key] === '')) {
        newErrors[field.key] = `${field.label} is required`;
      }
      
      if (field.type === 'email' && formData[field.key]) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData[field.key])) {
          newErrors[field.key] = 'Please enter a valid email address';
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setSubmitError(null);

    try {
      await onSubmit(formData);
    } catch (error: any) {
      setSubmitError(error.message || 'An error occurred while saving');
    } finally {
      setLoading(false);
    }
  };

  const renderField = (field: any) => {
    const value = formData[field.key] || '';
    const error = errors[field.key];

    switch (field.type) {
      case 'textarea':
        return (
          <Field key={field.key} error={error}>
            <FieldLabel required={field.required}>{field.label}</FieldLabel>
            <FieldInput>
              <Textarea
                value={value}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange(field.key, e.target.value)}
                placeholder={`Enter ${field.label.toLowerCase()}`}
              />
            </FieldInput>
            {error && <FieldError>{error}</FieldError>}
          </Field>
        );

      case 'number':
        return (
          <Field key={field.key} error={error}>
            <FieldLabel required={field.required}>{field.label}</FieldLabel>
            <FieldInput>
              <NumberInput
                value={value}
                onValueChange={(value: number | undefined) => handleInputChange(field.key, value)}
                placeholder={`Enter ${field.label.toLowerCase()}`}
              />
            </FieldInput>
            {error && <FieldError>{error}</FieldError>}
          </Field>
        );

      case 'email':
        return (
          <Field key={field.key} error={error}>
            <FieldLabel required={field.required}>{field.label}</FieldLabel>
            <FieldInput>
              <TextInput
                type="email"
                value={value}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange(field.key, e.target.value)}
                placeholder={`Enter ${field.label.toLowerCase()}`}
              />
            </FieldInput>
            {error && <FieldError>{error}</FieldError>}
          </Field>
        );

      case 'relation':
        return (
          <Field key={field.key} error={error}>
            <FieldLabel required={field.required}>{field.label}</FieldLabel>
            <FieldInput>
              <Select
                value={value}
                onChange={(value: string) => handleInputChange(field.key, value)}
                placeholder={`Select ${field.label.toLowerCase()}`}
              >
                <Option value="">Select {field.label}</Option>
                {/* TODO: Load relation options dynamically */}
              </Select>
            </FieldInput>
            {error && <FieldError>{error}</FieldError>}
          </Field>
        );

      case 'media':
        return (
          <Field key={field.key} error={error}>
            <FieldLabel required={field.required}>{field.label}</FieldLabel>
            <FieldInput>
              <Box
                padding={4}
                border="1px dashed"
                borderColor="neutral300"
                borderRadius="4px"
                textAlign="center"
              >
                <Typography variant="omega" textColor="neutral600">
                  Media upload will be implemented
                </Typography>
              </Box>
            </FieldInput>
            {error && <FieldError>{error}</FieldError>}
          </Field>
        );

      default:
        return (
          <Field key={field.key} error={error}>
            <FieldLabel required={field.required}>{field.label}</FieldLabel>
            <FieldInput>
              <TextInput
                value={value}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange(field.key, e.target.value)}
                placeholder={`Enter ${field.label.toLowerCase()}`}
              />
            </FieldInput>
            {error && <FieldError>{error}</FieldError>}
          </Field>
        );
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardBody>
          {submitError && (
            <Alert variant="danger" marginBottom={4} closeLabel="Close">
              {submitError}
            </Alert>
          )}

          <Grid gap={4}>
            {config.fields.map(renderField)}
          </Grid>

          <Flex justifyContent="flex-end" gap={2} marginTop={6}>
            <Button variant="tertiary" onClick={onCancel} disabled={loading}>
              Cancel
            </Button>
            <Button
              type="submit"
              loading={loading}
              startIcon={<Check />}
            >
              {mode === 'create' ? 'Create' : 'Update'} {config.singular}
            </Button>
          </Flex>
        </CardBody>
      </Card>
    </form>
  );
};

export default CollectionForm;
