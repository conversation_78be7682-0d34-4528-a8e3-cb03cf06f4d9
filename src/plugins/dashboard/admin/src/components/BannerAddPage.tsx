import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import BaseAddDialog from './shared/BaseAddDialog';
import SimpleMediaPicker from './shared/SimpleMediaPicker';

interface Banner {
  title: string;
  description: string;
  image: string;
  vendor: string;
  status: 'published' | 'draft';
}

const BannerAddPage = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [banner, setBanner] = useState<Banner>({
    title: '',
    description: '',
    image: '',
    vendor: '',
    status: 'draft'
  });
  const [isPublished, setIsPublished] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setBanner(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (data: any) => {
    setIsSubmitting(true);

    try {
      // Update banner status based on published switch
      const finalBanner = {
        ...banner,
        status: isPublished ? 'published' : 'draft'
      };

      // This would be replaced with an actual API call
      console.log('Submitting banner:', finalBanner);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Redirect to banners list with banners selected
      navigate('/plugins/dashboard?collection=banners');
    } catch (error) {
      console.error('Error creating banner:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/plugins/dashboard?collection=banners');
  };

  return (
    <BaseAddDialog
      title="Add New Banner"
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      isSubmitting={isSubmitting}
      showPublishedSwitch={false}
      initialPublished={isPublished}
      onPublishedChange={setIsPublished}
    >
      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(banner); }}>
        <div style={{
          display: 'grid',
          gap: '24px'
        }}>
          {/* Media Picker - First */}
          <div>
            <SimpleMediaPicker
              label="Image"
              value={banner.image}
              onChange={(value) => setBanner(prev => ({ ...prev, image: value }))}
            />
          </div>

          {/* Vendor - Second */}
          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: 'bold',
              marginBottom: '8px',
              color: 'white'
            }}>
              Vendor
            </label>
            <input
              type="text"
              name="vendor"
              value={banner.vendor}
              onChange={handleInputChange}
              style={{
                width: '100%',
                padding: '12px 16px',
                backgroundColor: '#181826',
                color: 'white',
                border: '1px solid #32324d',
                borderRadius: '6px',
                fontSize: '16px',
                outline: 'none'
              }}
              placeholder="Enter vendor name"
            />
          </div>

          {/* Title - Third */}
          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: 'bold',
              marginBottom: '8px',
              color: 'white'
            }}>
              Title *
            </label>
            <input
              type="text"
              name="title"
              value={banner.title}
              onChange={handleInputChange}
              required
              style={{
                width: '100%',
                padding: '12px 16px',
                backgroundColor: '#181826',
                color: 'white',
                border: '1px solid #32324d',
                borderRadius: '6px',
                fontSize: '16px',
                outline: 'none'
              }}
              placeholder="Enter banner title"
            />
          </div>

          {/* Description - Fourth */}
          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: 'bold',
              marginBottom: '8px',
              color: 'white'
            }}>
              Description
            </label>
            <textarea
              name="description"
              value={banner.description}
              onChange={handleInputChange}
              rows={4}
              style={{
                width: '100%',
                padding: '12px 16px',
                backgroundColor: '#181826',
                color: 'white',
                border: '1px solid #32324d',
                borderRadius: '6px',
                fontSize: '16px',
                outline: 'none',
                resize: 'vertical'
              }}
              placeholder="Enter banner description"
            />
          </div>
        </div>

        {/* Form Actions */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: '32px',
          paddingTop: '24px',
          borderTop: '1px solid #32324d'
        }}>
          {/* Published Switch - Left side */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}>
            <label style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              cursor: 'pointer',
              color: 'white',
              fontSize: '16px'
            }}>
              <div style={{
                position: 'relative',
                width: '48px',
                height: '24px',
                backgroundColor: isPublished ? '#4945ff' : '#32324d',
                borderRadius: '12px',
                transition: 'background-color 0.2s',
                cursor: 'pointer'
              }} onClick={() => setIsPublished(!isPublished)}>
                <div style={{
                  position: 'absolute',
                  top: '2px',
                  left: isPublished ? '26px' : '2px',
                  width: '20px',
                  height: '20px',
                  backgroundColor: 'white',
                  borderRadius: '50%',
                  transition: 'left 0.2s',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                }} />
              </div>
              <span>Published</span>
            </label>
          </div>

          {/* Buttons - Right side */}
          <div style={{
            display: 'flex',
            gap: '16px'
          }}>
            <button
              type="button"
              onClick={handleCancel}
              disabled={isSubmitting}
              style={{
                padding: '12px 24px',
                backgroundColor: 'transparent',
                color: '#a5a5ba',
                border: '1px solid #32324d',
                borderRadius: '6px',
                fontSize: '16px',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                opacity: isSubmitting ? 0.6 : 1
              }}
            >
              Cancel
            </button>

            <button
              type="submit"
              disabled={isSubmitting || !banner.title.trim()}
              style={{
                padding: '12px 24px',
                backgroundColor: isSubmitting || !banner.title.trim() ? '#32324d' : '#4945ff',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '16px',
                cursor: isSubmitting || !banner.title.trim() ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              {isSubmitting && (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid transparent',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
              )}
              {isSubmitting ? 'Creating...' : 'Create Banner'}
            </button>
          </div>
        </div>
      </form>
    </BaseAddDialog>
  );
};

export default BannerAddPage;
