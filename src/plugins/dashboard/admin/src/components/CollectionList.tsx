import { useState } from 'react';
import {
  Box,
  <PERSON>lex,
  <PERSON><PERSON>graphy,
  Button,
  IconButton,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Checkbox,
  Badge,
  Searchbar,
  Select,
  Option,
  Card,
  CardBody,
  Loader,
  EmptyStateLayout
} from '@strapi/design-system';
import {
  Filter,
  Plus
} from '@strapi/icons';

// Custom Font Awesome style icons with white colors
const SortUpIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M8 3L12 7H4L8 3Z" fill="white"/>
  </svg>
);

const SortDownIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M8 13L4 9H12L8 13Z" fill="white"/>
  </svg>
);

const EditIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M11.013 1.427a1.75 1.75 0 0 1 2.474 0l1.086 1.086a1.75 1.75 0 0 1 0 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 0 1-.927-.928l.929-3.25c.081-.286.235-.547.445-.758l8.61-8.61Z" fill="white"/>
    <path d="m5.21 8.842 1.948 1.948" stroke="white" strokeWidth="0.5"/>
  </svg>
);

const TrashIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M6.5 1h3a.5.5 0 0 1 .5.5v1H6v-1a.5.5 0 0 1 .5-.5ZM11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3A1.5 1.5 0 0 0 5 1.5v1H2.506a.5.5 0 0 0 0 1h.5v10A1.5 1.5 0 0 0 4.5 15h7a1.5 1.5 0 0 0 1.5-1.5v-10h.5a.5.5 0 0 0 0-1H11Z" fill="white"/>
    <path d="M6.5 5.5a.5.5 0 0 1 1 0v6a.5.5 0 0 1-1 0v-6ZM8.5 5.5a.5.5 0 0 1 1 0v6a.5.5 0 0 1-1 0v-6Z" fill="white"/>
  </svg>
);

const EyeIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M8 2C4.5 2 1.73 4.61 1 8c.73 3.39 3.5 6 7 6s6.27-2.61 7-6c-.73-3.39-3.5-6-7-6Z" stroke="white" strokeWidth="1.5" fill="none"/>
    <circle cx="8" cy="8" r="2.5" stroke="white" strokeWidth="1.5" fill="none"/>
  </svg>
);

const EyeOffIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M1 1l14 14M6.71 6.71a2 2 0 1 0 2.58 2.58" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
    <path d="M3.5 3.5C2.83 4.27 2.22 5.25 1.73 6.39c.49 1.14 1.1 2.12 1.77 2.89M12.5 12.5c.67-.77 1.28-1.75 1.77-2.89-.49-1.14-1.1-2.12-1.77-2.89" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

interface CollectionListProps {
  config: any;
  data: any[];
  loading: boolean;
  pagination: any;
  search: string;
  filters: any;
  selectedItems: string[];
  onSearch: (search: string) => void;
  onFilter: (filters: any) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onEdit: (item: any) => void;
  onDelete: (id: string) => void;
  onBulkDelete: () => void;
  onPublish: (id: string, publishedAt: string | null) => void;
  onSelectionChange: (selectedItems: string[]) => void;
  onSort?: (field: string, direction: 'asc' | 'desc' | null) => void;
}

const CollectionList = ({
  config,
  data,
  loading,
  pagination,
  search,
  selectedItems,
  onSearch,
  onPageChange,
  onPageSizeChange,
  onEdit,
  onDelete,
  onBulkDelete,
  onPublish,
  onSelectionChange,
  onSort
}: CollectionListProps) => {
  const [showFilters, setShowFilters] = useState(false);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc' | null>(null);
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    itemId: string | null;
    itemName: string;
    isBulk: boolean;
  }>({
    isOpen: false,
    itemId: null,
    itemName: '',
    isBulk: false
  });

  // Handle column sorting
  const handleSort = (field: string) => {
    let newDirection: 'asc' | 'desc' | null = 'asc';

    if (sortField === field) {
      if (sortDirection === 'asc') {
        newDirection = 'desc';
      } else if (sortDirection === 'desc') {
        newDirection = null;
      }
    }

    setSortField(newDirection ? field : null);
    setSortDirection(newDirection);

    if (onSort) {
      onSort(field, newDirection);
    }
  };

  // Get sort icon for column
  const getSortIcon = (field: string) => {
    if (sortField !== field) return null;
    if (sortDirection === 'asc') return <SortUpIcon />;
    if (sortDirection === 'desc') return <SortDownIcon />;
    return null;
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(data.map(item => item.id.toString()));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedItems, id]);
    } else {
      onSelectionChange(selectedItems.filter(item => item !== id));
    }
  };

  const handleDeleteClick = (item: any) => {
    const displayName = item.title || item.name || item.business_name || `Item ${item.id}`;
    setDeleteDialog({
      isOpen: true,
      itemId: item.id.toString(),
      itemName: displayName,
      isBulk: false
    });
  };

  const handleBulkDeleteClick = () => {
    setDeleteDialog({
      isOpen: true,
      itemId: null,
      itemName: `${selectedItems.length} items`,
      isBulk: true
    });
  };

  const handleDeleteConfirm = () => {
    if (deleteDialog.isBulk) {
      onBulkDelete();
    } else if (deleteDialog.itemId) {
      onDelete(deleteDialog.itemId);
    }
    setDeleteDialog({ isOpen: false, itemId: null, itemName: '', isBulk: false });
  };

  const handleDeleteCancel = () => {
    setDeleteDialog({ isOpen: false, itemId: null, itemName: '', isBulk: false });
  };

  const handleRowClick = (item: any) => {
    onEdit(item);
  };

  const getDisplayValue = (item: any, field: any) => {
    const value = item[field.key];

    if (!value) return '-';

    switch (field.type) {
      case 'media':
        // Handle both single media and array of media
        if (Array.isArray(value) && value.length > 0) {
          const firstImage = value[0];
          return firstImage.url ? (
            <img
              src={firstImage.url}
              alt={firstImage.name || 'Media'}
              style={{
                width: 40,
                height: 40,
                objectFit: 'cover',
                borderRadius: 4,
                cursor: 'pointer',
                border: '1px solid #ddd'
              }}
              onClick={(e) => {
                e.stopPropagation();
                window.open(firstImage.url, '_blank');
              }}
            />
          ) : '-';
        } else if (value.url) {
          return (
            <img
              src={value.url}
              alt={value.name || 'Media'}
              style={{
                width: 40,
                height: 40,
                objectFit: 'cover',
                borderRadius: 4,
                cursor: 'pointer',
                border: '1px solid #ddd'
              }}
              onClick={(e) => {
                e.stopPropagation();
                window.open(value.url, '_blank');
              }}
            />
          );
        }
        return '-';
      case 'relation':
        return value.name || value.title || value.business_name || `ID: ${value.id}`;
      case 'boolean':
        return value ? 'Yes' : 'No';
      default:
        return typeof value === 'string' && value.length > 50
          ? `${value.substring(0, 50)}...`
          : value;
    }
  };

  if (loading) {
    return (
      <Box padding={8}>
        <Loader>Loading {config.name}...</Loader>
      </Box>
    );
  }

  if (!data.length && !search) {
    return (
      <EmptyStateLayout
        icon={<Plus />}
        content={`No ${config.name.toLowerCase()} found`}
        action={
          <Button variant="secondary" startIcon={<Plus />}>
            Create your first {config.singular.toLowerCase()}
          </Button>
        }
      />
    );
  }

  return (
    <Box>
      {/* Search and Filters */}
      <Card marginBottom={4}>
        <CardBody>
          <Flex gap={4} alignItems="center">
            <Box flex="1">
              <Searchbar
                placeholder={`Search ${config.name.toLowerCase()}...`}
                value={search}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onSearch(e.target.value)}
                onClear={() => onSearch('')}
              />
            </Box>
            <Button
              variant="tertiary"
              startIcon={<Filter />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filters
            </Button>
          </Flex>
        </CardBody>
      </Card>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <Card marginBottom={4}>
          <CardBody>
            <Flex alignItems="center" justifyContent="space-between">
              <Typography variant="omega">
                {selectedItems.length} item(s) selected
              </Typography>
              <Button
                variant="danger-light"
                startIcon={<TrashIcon />}
                onClick={handleBulkDeleteClick}
              >
                Delete Selected
              </Button>
            </Flex>
          </CardBody>
        </Card>
      )}

      {/* Table */}
      <Card>
        <Table>
          <Thead>
            <Tr>
              <Th>
                <Checkbox
                  checked={selectedItems.length === data.length && data.length > 0}
                  indeterminate={selectedItems.length > 0 && selectedItems.length < data.length}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSelectAll(e.target.checked)}
                />
              </Th>
              <Th style={{ cursor: 'pointer' }} onClick={() => handleSort('id')}>
                <Flex alignItems="center" gap={1}>
                  <Typography variant="sigma">ID</Typography>
                  {getSortIcon('id')}
                </Flex>
              </Th>
              {config.fields.slice(0, 3).map((field: any) => (
                <Th key={field.key} style={{ cursor: 'pointer' }} onClick={() => handleSort(field.key)}>
                  <Flex alignItems="center" gap={1}>
                    <Typography variant="sigma">{field.label}</Typography>
                    {getSortIcon(field.key)}
                  </Flex>
                </Th>
              ))}
              <Th style={{ cursor: 'pointer' }} onClick={() => handleSort('publishedAt')}>
                <Flex alignItems="center" gap={1}>
                  <Typography variant="sigma">Status</Typography>
                  {getSortIcon('publishedAt')}
                </Flex>
              </Th>
              <Th style={{ cursor: 'pointer' }} onClick={() => handleSort('createdAt')}>
                <Flex alignItems="center" gap={1}>
                  <Typography variant="sigma">Created At</Typography>
                  {getSortIcon('createdAt')}
                </Flex>
              </Th>
              <Th>
                <Typography variant="sigma">Actions</Typography>
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {data.map((item) => (
              <Tr
                key={item.id}
                style={{ cursor: 'pointer' }}
                onClick={() => handleRowClick(item)}
              >
                <Td onClick={(e: React.MouseEvent) => e.stopPropagation()}>
                  <Checkbox
                    checked={selectedItems.includes(item.id.toString())}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSelectItem(item.id.toString(), e.target.checked)}
                  />
                </Td>
                <Td>
                  <Box>
                    <Typography variant="omega" fontWeight="bold">
                      {item.id}
                    </Typography>
                    <Typography variant="pi" textColor="neutral600" style={{ fontSize: '11px' }}>
                      {item.documentId}
                    </Typography>
                  </Box>
                </Td>
                {config.fields.slice(0, 3).map((field: any) => (
                  <Td key={field.key}>
                    {getDisplayValue(item, field)}
                  </Td>
                ))}
                <Td>
                  <Flex alignItems="center" gap={2}>
                    {item.publishedAt ? <EyeIcon /> : <EyeOffIcon />}
                    <Badge
                      variant={item.publishedAt ? 'success' : 'secondary'}
                      style={{
                        backgroundColor: 'transparent',
                        border: item.publishedAt ? '1px solid #2ecc71' : '1px solid #666',
                        color: item.publishedAt ? '#2ecc71' : '#666'
                      }}
                    >
                      {item.publishedAt ? 'Published' : 'Draft'}
                    </Badge>
                  </Flex>
                </Td>
                <Td>
                  <Typography variant="pi" textColor="neutral600">
                    {new Date(item.createdAt).toLocaleDateString()}
                  </Typography>
                </Td>
                <Td onClick={(e: React.MouseEvent) => e.stopPropagation()}>
                  <Flex gap={1}>
                    <IconButton
                      label="Edit"
                      onClick={() => onEdit(item)}
                      style={{ backgroundColor: '#4945ff' }}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      label={item.publishedAt ? 'Unpublish' : 'Publish'}
                      onClick={() => onPublish(item.id, item.publishedAt)}
                      style={{ backgroundColor: '#4945ff' }}
                    >
                      {item.publishedAt ? <EyeOffIcon /> : <EyeIcon />}
                    </IconButton>
                    <IconButton
                      label="Delete"
                      onClick={() => handleDeleteClick(item)}
                      style={{ backgroundColor: '#ee5a52' }}
                    >
                      <TrashIcon />
                    </IconButton>
                  </Flex>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Card>

      {/* Enhanced Footer with Pagination */}
      <Card marginTop={4}>
        <CardBody>
          <Flex justifyContent="space-between" alignItems="center">
            <Flex alignItems="center" gap={4}>
              <Typography variant="pi" textColor="neutral600">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to {Math.min(pagination.page * pagination.pageSize, pagination.total)} of {pagination.total} entries
              </Typography>

              <Flex alignItems="center" gap={2}>
                <Typography variant="pi" textColor="neutral600">Items per page:</Typography>
                <Select
                  value={pagination.pageSize.toString()}
                  onChange={(value: string) => onPageSizeChange(Number(value))}
                  size="S"
                >
                  <Option value="10">10</Option>
                  <Option value="20">20</Option>
                  <Option value="50">50</Option>
                  <Option value="100">100</Option>
                  <Option value="200">200</Option>
                  <Option value="500">500</Option>
                </Select>
              </Flex>
            </Flex>

            <Flex alignItems="center" gap={2}>
              <Button
                variant="tertiary"
                size="S"
                disabled={pagination.page === 1}
                onClick={() => onPageChange(pagination.page - 1)}
              >
                Previous
              </Button>

              {/* Page Numbers */}
              <Flex alignItems="center" gap={1}>
                {Array.from({ length: Math.min(5, pagination.pageCount) }, (_, i) => {
                  let pageNum;
                  if (pagination.pageCount <= 5) {
                    pageNum = i + 1;
                  } else if (pagination.page <= 3) {
                    pageNum = i + 1;
                  } else if (pagination.page >= pagination.pageCount - 2) {
                    pageNum = pagination.pageCount - 4 + i;
                  } else {
                    pageNum = pagination.page - 2 + i;
                  }

                  return (
                    <Button
                      key={pageNum}
                      variant={pagination.page === pageNum ? 'default' : 'tertiary'}
                      size="S"
                      onClick={() => onPageChange(pageNum)}
                      style={{ minWidth: '32px' }}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </Flex>

              <Button
                variant="tertiary"
                size="S"
                disabled={pagination.page === pagination.pageCount}
                onClick={() => onPageChange(pagination.page + 1)}
              >
                Next
              </Button>
            </Flex>
          </Flex>
        </CardBody>
      </Card>

      {/* Delete Confirmation Dialog */}
      {/* <ConfirmDialog
        isOpen={deleteDialog.isOpen}
        title={deleteDialog.isBulk ? 'Delete Multiple Items' : 'Delete Item'}
        message={`Are you sure you want to delete ${deleteDialog.itemName}? This action cannot be undone.`}
        confirmText="Delete"
        variant="danger"
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      /> */}
    </Box>
  );
};

export default CollectionList;
