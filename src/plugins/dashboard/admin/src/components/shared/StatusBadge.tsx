// Status badge with standard HTML/CSS

interface StatusBadgeProps {
  status: string;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'alternative';
}

const StatusBadge = ({ status, variant }: StatusBadgeProps) => {
  const getStyles = (status: string) => {
    const statusLower = status.toLowerCase();
    
    switch (statusLower) {
      case 'published':
      case 'active':
      case 'done':
      case 'confirmed':
        return {
          bg: 'transparent',
          color: '#4caf50',
          border: '1px solid #4caf50',
          filled: false,
          icon: '○'
        };
      case 'draft':
      case 'pending':
        return {
          bg: 'transparent',
          color: '#ff9800',
          border: '1px solid #ff9800',
          filled: false,
          icon: '○'
        };
      case 'inactive':
      case 'canceled':
      case 'refunded':
        return {
          bg: 'transparent',
          color: '#f44336',
          border: '1px solid #f44336',
          filled: false,
          icon: '○'
        };
      case 'delivering':
        return {
          bg: 'transparent',
          color: '#2196f3',
          border: '1px solid #2196f3',
          filled: false,
          icon: '○'
        };
      default:
        return {
          bg: 'transparent',
          color: '#9e9e9e',
          border: '1px solid #9e9e9e',
          filled: false,
          icon: '○'
        };
    }
  };

  const styles = getStyles(status);
  const displayText = status.charAt(0).toUpperCase() + status.slice(1);

  return (
    <span style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '6px',
      padding: '4px 12px',
      borderRadius: '16px',
      fontSize: '11px',
      fontWeight: '600',
      backgroundColor: styles.bg,
      color: styles.color,
      border: styles.border,
      textTransform: 'uppercase',
      letterSpacing: '0.5px',
      minWidth: '90px'
    }}>
      <span style={{
        fontSize: '12px',
        lineHeight: 1
      }}>
        {styles.icon}
      </span>
      {displayText}
    </span>
  );
};

export default StatusBadge;
