import { useState, useEffect } from 'react';
import { useFetchClient } from '@strapi/strapi/admin';
import CollectionList from './CollectionList';
import CollectionForm from './CollectionForm';

interface CollectionConfig {
  name: string;
  singular: string;
  apiEndpoint: string;
  fields: Array<{
    key: string;
    label: string;
    type: string;
    required?: boolean;
    multiple?: boolean;
    target?: string;
  }>;
}

interface CollectionManagerProps {
  config: CollectionConfig;
  mode: 'list' | 'create' | 'edit';
  selectedItem: any;
  onEdit: (item: any) => void;
  onBackToList: () => void;
}

const CollectionManager = ({
  config,
  mode,
  selectedItem,
  onEdit,
  onBackToList
}: CollectionManagerProps) => {
  const { get, post, put, del } = useFetchClient();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    pageCount: 0,
    total: 0
  });
  const [filters, setFilters] = useState({});
  const [search, setSearch] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc' | null>(null);

  const fetchData = async (page = 1, pageSize = 10, searchTerm = '', filterParams = {}, sortFieldParam: string | null = null, sortDirectionParam: 'asc' | 'desc' | null = null) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        'pagination[page]': page.toString(),
        'pagination[pageSize]': pageSize.toString(),
        'populate': '*',
        ...filterParams
      });

      if (searchTerm) {
        // Add search logic based on collection type
        if (config.fields.find(f => f.key === 'title')) {
          params.append('filters[title][$containsi]', searchTerm);
        } else if (config.fields.find(f => f.key === 'name')) {
          params.append('filters[name][$containsi]', searchTerm);
        }
      }

      // Add sorting
      if (sortFieldParam && sortDirectionParam) {
        params.append('sort', `${sortFieldParam}:${sortDirectionParam}`);
      }

      const { data: response } = await get(`${config.apiEndpoint}?${params}`);

      setData(response.data || []);
      setPagination(response.meta?.pagination || pagination);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (mode === 'list') {
      fetchData(pagination.page, pagination.pageSize, search, filters, sortField, sortDirection);
    }
  }, [mode, pagination.page, pagination.pageSize, search, filters, sortField, sortDirection]);

  const handleSort = (field: string, direction: 'asc' | 'desc' | null) => {
    setSortField(field);
    setSortDirection(direction);
  };

  const handleCreate = async (formData: any) => {
    try {
      await post(config.apiEndpoint, { data: formData });
      onBackToList();
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error creating item:', error);
      throw error;
    }
  };

  const handleUpdate = async (formData: any) => {
    try {
      await put(`${config.apiEndpoint}/${selectedItem.id}`, { data: formData });
      onBackToList();
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error updating item:', error);
      throw error;
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await del(`${config.apiEndpoint}/${id}`);
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error deleting item:', error);
      throw error;
    }
  };

  const handleBulkDelete = async () => {
    try {
      await Promise.all(
        selectedItems.map(id => del(`${config.apiEndpoint}/${id}`))
      );
      setSelectedItems([]);
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error bulk deleting items:', error);
    }
  };

  const handlePublish = async (id: string, publishedAt: string | null) => {
    try {
      const action = publishedAt ? 'unpublish' : 'publish';
      await post(`${config.apiEndpoint}/${id}/actions/${action}`);
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error publishing/unpublishing item:', error);
    }
  };

  if (mode === 'create' || mode === 'edit') {
    return (
      <CollectionForm
        config={config}
        mode={mode}
        initialData={selectedItem}
        onSubmit={mode === 'create' ? handleCreate : handleUpdate}
        onCancel={onBackToList}
      />
    );
  }

  return (
    <CollectionList
      config={config}
      data={data}
      loading={loading}
      pagination={pagination}
      search={search}
      filters={filters}
      selectedItems={selectedItems}
      onSearch={setSearch}
      onFilter={setFilters}
      onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
      onPageSizeChange={(pageSize) => setPagination(prev => ({ ...prev, pageSize, page: 1 }))}
      onEdit={onEdit}
      onDelete={handleDelete}
      onBulkDelete={handleBulkDelete}
      onPublish={handlePublish}
      onSelectionChange={setSelectedItems}
      onSort={handleSort}
    />
  );
};

export default CollectionManager;
