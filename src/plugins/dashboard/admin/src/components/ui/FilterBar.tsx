import { getTranslation } from '../../utils/getTranslation';
import { useSafeIntl } from '../../utils/contextSafety';
import VendorDropdown from './VendorDropdown';

interface AdvancedFilters {
  dateFrom: string;
  dateTo: string;
  status: string;
}

interface FilterBarProps {
  filters: {
    search: string;
    vendor: string;
    dateRange: any;
  };
  onFiltersChange: (filters: any) => void;
  selectedCollection: string;
  advancedFilters: AdvancedFilters;
  onAdvancedFiltersChange: (filters: AdvancedFilters) => void;
  showAdvancedFilters: boolean;
  onAdvancedFiltersToggle: (show: boolean) => void;
}

const FilterBar = ({
  filters,
  onFiltersChange,
  selectedCollection,
  advancedFilters,
  onAdvancedFiltersChange,
  showAdvancedFilters,
  onAdvancedFiltersToggle
}: FilterBarProps) => {
  const { formatMessage } = useSafeIntl();

  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value });
  };

  const handleVendorChange = (value: string) => {
    onFiltersChange({ vendor: value });
  };

  const handleResetFilters = () => {
    onFiltersChange({
      search: '',
      vendor: 'all',
      dateRange: null
    });
    onAdvancedFiltersChange({
      dateFrom: '',
      dateTo: '',
      status: 'published'
    });
  };

  const handleAdvancedFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    onAdvancedFiltersChange({
      ...advancedFilters,
      [name]: value
    });
  };

  // Validate date range
  const isDateRangeValid = () => {
    if (!advancedFilters.dateFrom || !advancedFilters.dateTo) return true;

    const fromDate = new Date(advancedFilters.dateFrom);
    const toDate = new Date(advancedFilters.dateTo);

    return fromDate <= toDate;
  };

  return (
    <div style={{
      backgroundColor: '#212134',
      padding: '16px',
      borderRadius: '4px',
      boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)',
      marginBottom: '24px'
    }}>
      <div style={{
        display: 'flex',
        gap: '16px',
        alignItems: 'end'
      }}>
        {/* Search */}
        <div style={{ flex: 1 }}>
          <label style={{
            display: 'block',
            fontSize: '12px',
            fontWeight: 'bold',
            marginBottom: '4px',
            color: 'white'
          }}>
            {formatMessage({
              id: getTranslation('filters.search.label'),
              defaultMessage: 'Search'
            })}
          </label>
          <input
            type="text"
            placeholder={formatMessage({
              id: getTranslation('filters.search.placeholder'),
              defaultMessage: 'Search in all fields...'
            })}
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #32324d',
              borderRadius: '4px',
              fontSize: '14px',
              outline: 'none',
              backgroundColor: '#181826',
              color: 'white'
            }}
          />
        </div>

        {/* Vendor Filter */}
        <div style={{ minWidth: '250px' }}>
          <VendorDropdown
            value={filters.vendor}
            onChange={handleVendorChange}
            label={formatMessage({
              id: getTranslation('filters.vendor.label'),
              defaultMessage: 'Vendor'
            })}
            placeholder={formatMessage({
              id: getTranslation('filters.vendor.placeholder'),
              defaultMessage: 'Select vendor'
            })}
          />
        </div>

        {/* Actions */}
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={handleResetFilters}
            style={{
              padding: '8px 16px',
              border: '1px solid #32324d',
              borderRadius: '4px',
              backgroundColor: '#181826',
              color: 'white',
              fontSize: '14px',
              cursor: 'pointer',
              outline: 'none'
            }}
          >
            {formatMessage({
              id: getTranslation('filters.reset'),
              defaultMessage: 'Reset'
            })}
          </button>

          <button
            onClick={() => onAdvancedFiltersToggle(!showAdvancedFilters)}
            style={{
              padding: '8px 16px',
              border: '1px solid #4945ff',
              borderRadius: '4px',
              backgroundColor: showAdvancedFilters ? 'transparent' : '#4945ff',
              color: showAdvancedFilters ? '#4945ff' : '#ffffff',
              fontSize: '14px',
              cursor: 'pointer',
              outline: 'none',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              if (showAdvancedFilters) {
                e.currentTarget.style.backgroundColor = '#4945ff';
                e.currentTarget.style.color = '#ffffff';
              }
            }}
            onMouseLeave={(e) => {
              if (showAdvancedFilters) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#4945ff';
              }
            }}
          >
            {formatMessage({
              id: getTranslation('filters.advanced'),
              defaultMessage: 'Advanced'
            })}
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div style={{
          marginTop: '16px',
          padding: '16px',
          backgroundColor: '#181826',
          borderRadius: '4px',
          border: '1px solid #32324d'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px'
          }}>
            <div>
              <label style={{
                display: 'block',
                fontSize: '12px',
                fontWeight: 'bold',
                marginBottom: '4px',
                color: 'white'
              }}>
                Date From
              </label>
              <input
                type="date"
                name="dateFrom"
                value={advancedFilters.dateFrom}
                onChange={handleAdvancedFilterChange}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  backgroundColor: '#212134',
                  color: 'white',
                  border: '1px solid #32324d',
                  borderRadius: '4px',
                  fontSize: '14px',
                  outline: 'none',
                  colorScheme: 'dark',
                  cursor: 'pointer'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                fontSize: '12px',
                fontWeight: 'bold',
                marginBottom: '4px',
                color: 'white'
              }}>
                Date To
              </label>
              <input
                type="date"
                name="dateTo"
                value={advancedFilters.dateTo}
                onChange={handleAdvancedFilterChange}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  backgroundColor: '#212134',
                  color: 'white',
                  border: '1px solid #32324d',
                  borderRadius: '4px',
                  fontSize: '14px',
                  outline: 'none',
                  colorScheme: 'dark',
                  cursor: 'pointer'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                fontSize: '12px',
                fontWeight: 'bold',
                marginBottom: '4px',
                color: 'white'
              }}>
                Status
              </label>
              <select
                name="status"
                value={advancedFilters.status}
                onChange={handleAdvancedFilterChange}
                style={{
                  width: '100%',
                  padding: '8px 16px 8px 12px',
                  backgroundColor: '#212134',
                  color: 'white',
                  border: '1px solid #32324d',
                  borderRadius: '4px',
                  fontSize: '14px',
                  outline: 'none',
                  cursor: 'pointer'
                }}
              >
                <option value="">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
              </select>
            </div>
          </div>

          {!isDateRangeValid() && (
            <div style={{
              marginTop: '8px',
              padding: '8px 12px',
              backgroundColor: '#d32f2f',
              color: 'white',
              borderRadius: '4px',
              fontSize: '14px'
            }}>
              Date From must be before Date To
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FilterBar;
