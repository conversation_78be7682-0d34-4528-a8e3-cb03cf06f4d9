// Action buttons with improved icons and styling

interface ActionButtonsProps {
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  size?: 'S' | 'M' | 'L';
  variant?: 'icon' | 'button';
  isExpanded?: boolean;
}

const ActionButtons = ({
  onView,
  onEdit,
  onDelete,
  size = 'S',
  variant = 'icon',
  isExpanded = false
}: ActionButtonsProps) => {
  // SVG icons for better visual appearance
  const icons = {
    view: {
      expanded: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 15L12 9L6 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
      collapsed: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
    edit: (
      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    ),
    delete: (
      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3 6H5H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M10 11V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M14 11V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    )
  };

  const buttonStyle = {
    padding: variant === 'icon' ? '8px' : '8px 12px',
    border: 'none',
    borderRadius: '4px',
    fontSize: '14px',
    cursor: 'pointer',
    outline: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '6px',
    minWidth: variant === 'icon' ? '32px' : 'auto',
    minHeight: variant === 'icon' ? '32px' : 'auto'
  };

  if (variant === 'icon') {
    return (
      <div style={{ display: 'flex', gap: '6px' }}>
        {onView && (
          <button
            onClick={onView}
            title={isExpanded ? "Collapse details" : "View details"}
            style={{
              ...buttonStyle,
              backgroundColor: isExpanded ? '#4945ff' : '#181826',
              color: isExpanded ? 'white' : '#a5a5ba',
              border: `1px solid ${isExpanded ? '#4945ff' : '#32324d'}`
            }}
            onMouseEnter={(e) => {
              if (!isExpanded) {
                e.currentTarget.style.backgroundColor = '#32324d';
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              if (!isExpanded) {
                e.currentTarget.style.backgroundColor = '#181826';
                e.currentTarget.style.color = '#a5a5ba';
              }
            }}
          >
            {isExpanded ? icons.view.expanded : icons.view.collapsed}
          </button>
        )}
        {onEdit && (
          <button
            onClick={onEdit}
            title="Edit"
            style={{
              ...buttonStyle,
              backgroundColor: '#181826',
              color: '#a5a5ba',
              border: '1px solid #32324d'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#32324d';
              e.currentTarget.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#181826';
              e.currentTarget.style.color = '#a5a5ba';
            }}
          >
            {icons.edit}
          </button>
        )}
        {onDelete && (
          <button
            onClick={onDelete}
            title="Delete"
            style={{
              ...buttonStyle,
              backgroundColor: '#181826',
              color: '#ff5252',
              border: '1px solid #32324d'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#d32f2f';
              e.currentTarget.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#181826';
              e.currentTarget.style.color = '#ff5252';
            }}
          >
            {icons.delete}
          </button>
        )}
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', gap: '8px' }}>
      {onView && (
        <button
          onClick={onView}
          style={{
            ...buttonStyle,
            backgroundColor: '#181826',
            color: 'white',
            border: '1px solid #32324d'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#32324d';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#181826';
          }}
        >
          {isExpanded ? icons.view.expanded : icons.view.collapsed}
          <span>{isExpanded ? "Collapse" : "View"}</span>
        </button>
      )}
      {onEdit && (
        <button
          onClick={onEdit}
          style={{
            ...buttonStyle,
            backgroundColor: '#4945ff',
            color: 'white',
            border: '1px solid #4945ff'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#3832e1';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#4945ff';
          }}
        >
          {icons.edit}
          <span>Edit</span>
        </button>
      )}
      {onDelete && (
        <button
          onClick={onDelete}
          style={{
            ...buttonStyle,
            backgroundColor: '#181826',
            color: '#ff5252',
            border: '1px solid #32324d'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#d32f2f';
            e.currentTarget.style.color = 'white';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#181826';
            e.currentTarget.style.color = '#ff5252';
          }}
        >
          {icons.delete}
          <span>Delete</span>
        </button>
      )}
    </div>
  );
};

export default ActionButtons;
