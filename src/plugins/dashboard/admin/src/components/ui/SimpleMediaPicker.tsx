import React, { useState, useRef } from 'react';

interface SimpleMediaPickerProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
}

const SimpleMediaPicker = ({ value, onChange, label }: SimpleMediaPickerProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(value);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Mock media library
  const mockImages = [
    'https://via.placeholder.com/800x400/4945ff/ffffff?text=Banner+1',
    'https://via.placeholder.com/800x400/32324d/ffffff?text=Banner+2',
    'https://via.placeholder.com/800x400/d32f2f/ffffff?text=Banner+3',
    'https://via.placeholder.com/800x400/2e7d32/ffffff?text=Banner+4',
    'https://via.placeholder.com/800x400/f57c00/ffffff?text=Banner+5',
    'https://via.placeholder.com/800x400/7b1fa2/ffffff?text=Banner+6'
  ];

  const handleImageSelect = (imageUrl: string) => {
    setPreviewUrl(imageUrl);
    onChange(imageUrl);
    setIsModalOpen(false);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      onChange(url);
      setIsModalOpen(false);
    }
  };

  const handleRemove = () => {
    setPreviewUrl('');
    onChange('');
  };

  return (
    <div>
      {label && (
        <div style={{ marginBottom: '12px' }}>
          <label style={{
            display: 'block',
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '4px',
            color: 'white'
          }}>
            {label}
          </label>
          <label style={{
            display: 'block',
            fontSize: '12px',
            fontWeight: 'normal',
            color: '#a5a5ba'
          }}>
            Media
          </label>
        </div>
      )}

      {/* Media Preview */}
      <div style={{
        width: '100%',
        height: '200px',
        borderRadius: '8px',
        backgroundColor: '#181826',
        border: '2px dashed #32324d',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        marginBottom: '12px'
      }}>
        {previewUrl ? (
          <>
            <img
              src={previewUrl}
              alt="Preview"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain'
              }}
            />
            <button
              onClick={handleRemove}
              style={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                width: '28px',
                height: '28px',
                borderRadius: '50%',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                border: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              ✕
            </button>
          </>
        ) : (
          <div style={{
            textAlign: 'center',
            color: '#a5a5ba'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '8px' }}>📷</div>
            <div style={{ fontSize: '14px' }}>No media selected</div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div style={{
        display: 'flex',
        gap: '8px'
      }}>
        <button
          onClick={() => fileInputRef.current?.click()}
          style={{
            flex: 1,
            padding: '10px 16px',
            backgroundColor: '#4945ff',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px'
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M21 15V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V15" stroke="currentColor" strokeWidth="2"/>
            <path d="M17 8L12 3L7 8" stroke="currentColor" strokeWidth="2"/>
            <path d="M12 3V15" stroke="currentColor" strokeWidth="2"/>
          </svg>
          Upload File
        </button>

        <button
          onClick={() => setIsModalOpen(true)}
          style={{
            flex: 1,
            padding: '10px 16px',
            backgroundColor: 'transparent',
            color: '#a5a5ba',
            border: '1px solid #32324d',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px'
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z" stroke="currentColor" strokeWidth="2"/>
            <path d="M8.5 10C9.32843 10 10 9.32843 10 8.5C10 7.67157 9.32843 7 8.5 7C7.67157 7 7 7.67157 7 8.5C7 9.32843 7.67157 10 8.5 10Z" stroke="currentColor" strokeWidth="2"/>
            <path d="M21 15L16 10L5 21" stroke="currentColor" strokeWidth="2"/>
          </svg>
          Browse Files
        </button>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        style={{ display: 'none' }}
      />

      {/* Media Library Modal */}
      {isModalOpen && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000
        }}>
          <div style={{
            backgroundColor: '#212134',
            borderRadius: '8px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '80vh',
            border: '1px solid #32324d',
            overflow: 'hidden'
          }}>
            {/* Modal Header */}
            <div style={{
              padding: '16px 24px',
              borderBottom: '1px solid #32324d',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h3 style={{
                fontSize: '18px',
                fontWeight: 'bold',
                color: 'white',
                margin: 0
              }}>
                Select Media
              </h3>
              <button
                onClick={() => setIsModalOpen(false)}
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: '#a5a5ba',
                  fontSize: '20px',
                  cursor: 'pointer',
                  width: '32px',
                  height: '32px',
                  borderRadius: '4px'
                }}
              >
                ✕
              </button>
            </div>

            {/* Media Grid */}
            <div style={{
              padding: '24px',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
              gap: '16px',
              maxHeight: '400px',
              overflowY: 'auto'
            }}>
              {mockImages.map((imageUrl, index) => (
                <div
                  key={index}
                  onClick={() => handleImageSelect(imageUrl)}
                  style={{
                    aspectRatio: '16/9',
                    borderRadius: '8px',
                    overflow: 'hidden',
                    cursor: 'pointer',
                    border: '2px solid transparent',
                    transition: 'border-color 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#4945ff';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'transparent';
                  }}
                >
                  <img
                    src={imageUrl}
                    alt={`Media ${index + 1}`}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleMediaPicker;
