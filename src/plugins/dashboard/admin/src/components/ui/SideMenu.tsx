import { getTranslation } from '../../utils/getTranslation';
import { useSafeIntl } from '../../utils/contextSafety';

interface SideMenuProps {
  selectedCollection: string;
  onCollectionChange: (collection: string) => void;
}

const SideMenu = ({ selectedCollection, onCollectionChange }: SideMenuProps) => {
  const { formatMessage } = useSafeIntl();

  const menuItems = [
    {
      id: 'overview',
      label: 'Overview',
      icon: '📊',
      category: 'main'
    },
    {
      id: 'products',
      label: 'Products',
      icon: '📦',
      category: 'commerce',
      count: 156
    },
    {
      id: 'orders',
      label: 'Orders',
      icon: '🛒',
      category: 'commerce',
      count: 89
    },
    {
      id: 'vendors',
      label: 'Vendors',
      icon: '🏪',
      category: 'commerce',
      count: 23
    },
    {
      id: 'clients',
      label: 'Clients',
      icon: '👥',
      category: 'users',
      count: 342
    },
    {
      id: 'messages',
      label: 'Messages',
      icon: '💬',
      category: 'communication',
      count: 45
    },
    {
      id: 'tasks',
      label: 'Tasks',
      icon: '✅',
      category: 'management',
      count: 12
    },
    {
      id: 'cities',
      label: 'Cities',
      icon: '🏙️',
      category: 'location',
      count: 67
    },
    {
      id: 'countries',
      label: 'Countries',
      icon: '🌍',
      category: 'location',
      count: 15
    },
    {
      id: 'currencies',
      label: 'Currencies',
      icon: '💰',
      category: 'settings',
      count: 8
    },
    {
      id: 'shippings',
      label: 'Shippings',
      icon: '🚚',
      category: 'commerce',
      count: 5
    },
    {
      id: 'banners',
      label: 'Banners',
      icon: '🖼️',
      category: 'content',
      count: 12
    },
    {
      id: 'configs',
      label: 'Configs',
      icon: '⚙️',
      category: 'settings',
      count: 3
    }
  ];

  const categories = {
    main: 'Main',
    commerce: 'Commerce',
    users: 'Users',
    communication: 'Communication',
    management: 'Management',
    location: 'Location',
    content: 'Content',
    settings: 'Settings'
  };

  const groupedItems = menuItems.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, typeof menuItems>);

  return (
    <div style={{
      backgroundColor: '#212134',
      padding: '16px',
      borderRadius: '4px',
      boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)',
      height: 'fit-content'
    }}>
      <h3 style={{
        fontSize: '18px',
        fontWeight: 'bold',
        marginBottom: '16px',
        color: 'white',
        margin: '0 0 16px 0'
      }}>
        {formatMessage({
          id: getTranslation('sidemenu.title'),
          defaultMessage: 'Collections'
        })}
      </h3>

      {Object.entries(groupedItems).map(([category, items], categoryIndex) => (
        <div key={category} style={{ marginBottom: '16px' }}>
          {category !== 'main' && (
            <>
              {categoryIndex > 0 && (
                <hr style={{
                  border: 'none',
                  borderTop: '1px solid #32324d',
                  margin: '12px 0'
                }} />
              )}
              <h4 style={{
                fontSize: '11px',
                fontWeight: 'bold',
                color: '#a5a5ba',
                textTransform: 'uppercase',
                marginBottom: '8px',
                margin: '0 0 8px 0'
              }}>
                {categories[category as keyof typeof categories]}
              </h4>
            </>
          )}

          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            {items.map((item) => {
              const isSelected = selectedCollection === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => onCollectionChange(item.id)}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: 'none',
                    borderRadius: '4px',
                    backgroundColor: isSelected ? '#4945ff' : 'transparent',
                    color: isSelected ? '#ffffff' : 'white',
                    fontSize: '14px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    outline: 'none',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!isSelected) {
                      e.currentTarget.style.backgroundColor = '#32324d';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isSelected) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ fontSize: '16px' }}>{item.icon}</span>
                    <span>{item.label}</span>
                  </div>
                  {item.count && (
                    <span style={{
                      fontSize: '12px',
                      backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : '#32324d',
                      color: isSelected ? '#ffffff' : '#a5a5ba',
                      padding: '2px 8px',
                      borderRadius: '12px'
                    }}>
                      {item.count}
                    </span>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SideMenu;
