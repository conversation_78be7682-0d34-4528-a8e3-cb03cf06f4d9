import { useState, useRef, useEffect } from 'react';

interface MediaFile {
  id: string;
  name: string;
  url: string;
  mime: string;
  size: number;
  createdAt: string;
}

interface MediaPickerProps {
  value: MediaFile | MediaFile[] | null;
  onChange: (value: MediaFile | MediaFile[] | null) => void;
  multiple?: boolean;
  label?: string;
  allowedTypes?: string[];
  maxFiles?: number;
  maxSize?: number; // in MB
}

const MediaPicker = ({
  value,
  onChange,
  multiple = false,
  label = 'Media',
  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  maxFiles = 5,
  maxSize = 10 // 10MB
}: MediaPickerProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMedia, setSelectedMedia] = useState<MediaFile[]>([]);
  const [activeTab, setActiveTab] = useState<'browse' | 'upload'>('browse');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Mock media library data - this will be replaced with real API data
  const mockMediaLibrary: MediaFile[] = [
    {
      id: '1',
      name: 'banner-1.jpg',
      url: 'https://via.placeholder.com/800x400?text=Banner+1',
      mime: 'image/jpeg',
      size: 1024 * 1024 * 2, // 2MB
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      name: 'product-image.png',
      url: 'https://via.placeholder.com/500x500?text=Product',
      mime: 'image/png',
      size: 1024 * 1024 * 1.5, // 1.5MB
      createdAt: '2024-01-10'
    },
    {
      id: '3',
      name: 'logo.svg',
      url: 'https://via.placeholder.com/200x200?text=Logo',
      mime: 'image/svg+xml',
      size: 1024 * 50, // 50KB
      createdAt: '2024-01-05'
    },
    {
      id: '4',
      name: 'background.jpg',
      url: 'https://via.placeholder.com/1920x1080?text=Background',
      mime: 'image/jpeg',
      size: 1024 * 1024 * 3, // 3MB
      createdAt: '2024-01-20'
    },
    {
      id: '5',
      name: 'icon.png',
      url: 'https://via.placeholder.com/64x64?text=Icon',
      mime: 'image/png',
      size: 1024 * 20, // 20KB
      createdAt: '2024-01-18'
    },
    {
      id: '6',
      name: 'banner-2.jpg',
      url: 'https://via.placeholder.com/800x400?text=Banner+2',
      mime: 'image/jpeg',
      size: 1024 * 1024 * 1.8, // 1.8MB
      createdAt: '2024-01-22'
    }
  ];

  // Filter media based on search term and allowed types
  const filteredMedia = mockMediaLibrary.filter(media => 
    (media.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
     media.mime.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (allowedTypes.length === 0 || allowedTypes.includes(media.mime))
  );

  // Initialize selected media from value prop
  useEffect(() => {
    if (value) {
      if (multiple && Array.isArray(value)) {
        setSelectedMedia(value);
      } else if (!multiple && !Array.isArray(value)) {
        setSelectedMedia([value]);
      } else {
        setSelectedMedia([]);
      }
    } else {
      setSelectedMedia([]);
    }
  }, [value, multiple]);

  // Handle media selection
  const handleMediaSelect = (media: MediaFile) => {
    if (multiple) {
      const isSelected = selectedMedia.some(item => item.id === media.id);
      
      if (isSelected) {
        // Remove from selection
        setSelectedMedia(selectedMedia.filter(item => item.id !== media.id));
      } else {
        // Add to selection if under max limit
        if (selectedMedia.length < maxFiles) {
          setSelectedMedia([...selectedMedia, media]);
        }
      }
    } else {
      // Single selection mode
      setSelectedMedia([media]);
    }
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Mock file upload - in real implementation, this would call an API
    const newMedia: MediaFile[] = [];
    
    Array.from(files).forEach((file, index) => {
      // Check file type
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
        console.error(`File type ${file.type} not allowed`);
        return;
      }
      
      // Check file size
      if (file.size > maxSize * 1024 * 1024) {
        console.error(`File size exceeds ${maxSize}MB limit`);
        return;
      }

      // Create a mock media object
      const newFile: MediaFile = {
        id: `new-${Date.now()}-${index}`,
        name: file.name,
        url: URL.createObjectURL(file),
        mime: file.type,
        size: file.size,
        createdAt: new Date().toISOString().split('T')[0]
      };
      
      newMedia.push(newFile);
    });

    // Add new media to selection
    if (multiple) {
      const totalFiles = selectedMedia.length + newMedia.length;
      if (totalFiles <= maxFiles) {
        setSelectedMedia([...selectedMedia, ...newMedia]);
      } else {
        setSelectedMedia([...selectedMedia, ...newMedia.slice(0, maxFiles - selectedMedia.length)]);
        console.error(`Only ${maxFiles} files can be selected`);
      }
    } else {
      setSelectedMedia([newMedia[0]]);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Apply selection and close modal
  const handleApply = () => {
    if (multiple) {
      onChange(selectedMedia);
    } else {
      onChange(selectedMedia.length > 0 ? selectedMedia[0] : null);
    }
    setIsModalOpen(false);
  };

  // Cancel selection and close modal
  const handleCancel = () => {
    // Reset to original value
    if (value) {
      if (multiple && Array.isArray(value)) {
        setSelectedMedia(value);
      } else if (!multiple && !Array.isArray(value)) {
        setSelectedMedia([value]);
      } else {
        setSelectedMedia([]);
      }
    } else {
      setSelectedMedia([]);
    }
    setIsModalOpen(false);
  };

  // Remove a selected media item
  const handleRemove = (mediaId: string) => {
    const newSelection = selectedMedia.filter(media => media.id !== mediaId);
    setSelectedMedia(newSelection);
    
    if (multiple) {
      onChange(newSelection.length > 0 ? newSelection : []);
    } else {
      onChange(newSelection.length > 0 ? newSelection[0] : null);
    }
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  return (
    <div>
      {/* Label */}
      {label && (
        <label style={{
          display: 'block',
          fontSize: '12px',
          fontWeight: 'bold',
          marginBottom: '4px',
          color: 'white'
        }}>
          {label}
        </label>
      )}

      {/* Media Preview Area */}
      <div style={{
        marginBottom: '8px',
        display: 'flex',
        flexWrap: 'wrap',
        gap: '8px'
      }}>
        {selectedMedia.length > 0 ? (
          selectedMedia.map(media => (
            <div 
              key={media.id}
              style={{
                position: 'relative',
                width: '100px',
                height: '100px',
                borderRadius: '4px',
                overflow: 'hidden',
                border: '1px solid #32324d'
              }}
            >
              <img 
                src={media.url} 
                alt={media.name}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
              <button
                onClick={() => handleRemove(media.id)}
                style={{
                  position: 'absolute',
                  top: '4px',
                  right: '4px',
                  width: '20px',
                  height: '20px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  border: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  fontSize: '12px',
                  padding: 0
                }}
              >
                ✕
              </button>
            </div>
          ))
        ) : (
          <div style={{
            width: '100px',
            height: '100px',
            borderRadius: '4px',
            backgroundColor: '#181826',
            border: '1px dashed #32324d',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#a5a5ba',
            fontSize: '24px'
          }}>
            <span>📷</span>
          </div>
        )}
      </div>

      {/* Media Picker Button */}
      <button
        onClick={() => setIsModalOpen(true)}
        style={{
          padding: '8px 16px',
          backgroundColor: '#181826',
          color: 'white',
          border: '1px solid #32324d',
          borderRadius: '4px',
          fontSize: '14px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M8.5 10C9.32843 10 10 9.32843 10 8.5C10 7.67157 9.32843 7 8.5 7C7.67157 7 7 7.67157 7 8.5C7 9.32843 7.67157 10 8.5 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M21 15L16 10L5 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        {multiple ? 'Select Media Files' : 'Select Media File'}
      </button>

      {/* Media Picker Modal */}
      {isModalOpen && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000
        }}>
          <div style={{
            backgroundColor: '#212134',
            borderRadius: '8px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            display: 'flex',
            flexDirection: 'column',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5)',
            border: '1px solid #32324d',
            overflow: 'hidden'
          }}>
            {/* Modal Header */}
            <div style={{
              padding: '16px 24px',
              borderBottom: '1px solid #32324d',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h3 style={{
                fontSize: '18px',
                fontWeight: 'bold',
                color: 'white',
                margin: 0
              }}>
                {multiple ? 'Select Media Files' : 'Select Media File'}
              </h3>
              <button
                onClick={handleCancel}
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: '#a5a5ba',
                  fontSize: '20px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '32px',
                  height: '32px',
                  borderRadius: '4px'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#32324d';
                  e.currentTarget.style.color = 'white';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = '#a5a5ba';
                }}
              >
                ✕
              </button>
            </div>

            {/* Modal Tabs */}
            <div style={{
              display: 'flex',
              borderBottom: '1px solid #32324d'
            }}>
              <button
                onClick={() => setActiveTab('browse')}
                style={{
                  padding: '12px 24px',
                  backgroundColor: activeTab === 'browse' ? '#32324d' : 'transparent',
                  color: activeTab === 'browse' ? 'white' : '#a5a5ba',
                  border: 'none',
                  borderBottom: activeTab === 'browse' ? '2px solid #4945ff' : 'none',
                  fontSize: '14px',
                  fontWeight: activeTab === 'browse' ? 'bold' : 'normal',
                  cursor: 'pointer',
                  flex: 1
                }}
              >
                Browse Media
              </button>
              <button
                onClick={() => setActiveTab('upload')}
                style={{
                  padding: '12px 24px',
                  backgroundColor: activeTab === 'upload' ? '#32324d' : 'transparent',
                  color: activeTab === 'upload' ? 'white' : '#a5a5ba',
                  border: 'none',
                  borderBottom: activeTab === 'upload' ? '2px solid #4945ff' : 'none',
                  fontSize: '14px',
                  fontWeight: activeTab === 'upload' ? 'bold' : 'normal',
                  cursor: 'pointer',
                  flex: 1
                }}
              >
                Upload New
              </button>
            </div>

            {/* Modal Content */}
            <div style={{
              padding: '16px 24px',
              flex: 1,
              overflowY: 'auto',
              maxHeight: 'calc(90vh - 180px)'
            }}>
              {activeTab === 'browse' ? (
                <>
                  {/* Search Bar */}
                  <div style={{ marginBottom: '16px' }}>
                    <input
                      type="text"
                      placeholder="Search media..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px 16px',
                        backgroundColor: '#181826',
                        color: 'white',
                        border: '1px solid #32324d',
                        borderRadius: '4px',
                        fontSize: '14px',
                        outline: 'none'
                      }}
                    />
                  </div>

                  {/* Media Grid */}
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
                    gap: '16px'
                  }}>
                    {filteredMedia.map(media => {
                      const isSelected = selectedMedia.some(item => item.id === media.id);
                      
                      return (
                        <div
                          key={media.id}
                          onClick={() => handleMediaSelect(media)}
                          style={{
                            cursor: 'pointer',
                            borderRadius: '4px',
                            overflow: 'hidden',
                            border: `2px solid ${isSelected ? '#4945ff' : 'transparent'}`,
                            backgroundColor: '#181826',
                            position: 'relative'
                          }}
                        >
                          <div style={{
                            width: '100%',
                            paddingTop: '75%', // 4:3 aspect ratio
                            position: 'relative',
                            backgroundColor: '#32324d'
                          }}>
                            <img
                              src={media.url}
                              alt={media.name}
                              style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover'
                              }}
                            />
                            
                            {isSelected && (
                              <div style={{
                                position: 'absolute',
                                top: '8px',
                                right: '8px',
                                width: '24px',
                                height: '24px',
                                borderRadius: '50%',
                                backgroundColor: '#4945ff',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'white',
                                fontSize: '14px'
                              }}>
                                ✓
                              </div>
                            )}
                          </div>
                          
                          <div style={{
                            padding: '8px',
                            borderTop: '1px solid #32324d'
                          }}>
                            <div style={{
                              fontSize: '12px',
                              fontWeight: '500',
                              color: 'white',
                              marginBottom: '4px',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}>
                              {media.name}
                            </div>
                            <div style={{
                              fontSize: '10px',
                              color: '#a5a5ba',
                              display: 'flex',
                              justifyContent: 'space-between'
                            }}>
                              <span>{formatFileSize(media.size)}</span>
                              <span>{media.mime.split('/')[1]}</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {filteredMedia.length === 0 && (
                    <div style={{
                      padding: '32px',
                      textAlign: 'center',
                      color: '#a5a5ba'
                    }}>
                      No media files found. Try a different search or upload new files.
                    </div>
                  )}
                </>
              ) : (
                <>
                  {/* Upload Area */}
                  <div
                    style={{
                      border: '2px dashed #32324d',
                      borderRadius: '8px',
                      padding: '32px',
                      textAlign: 'center',
                      backgroundColor: '#181826',
                      cursor: 'pointer'
                    }}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <div style={{
                      fontSize: '48px',
                      marginBottom: '16px',
                      color: '#4945ff'
                    }}>
                      ↑
                    </div>
                    <div style={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      color: 'white',
                      marginBottom: '8px'
                    }}>
                      Drag & drop files here or click to browse
                    </div>
                    <div style={{
                      fontSize: '14px',
                      color: '#a5a5ba',
                      marginBottom: '16px'
                    }}>
                      {allowedTypes.length > 0 ? (
                        <>Allowed types: {allowedTypes.map(type => type.split('/')[1]).join(', ')}</>
                      ) : (
                        'All file types allowed'
                      )}
                    </div>
                    <div style={{
                      fontSize: '14px',
                      color: '#a5a5ba'
                    }}>
                      Maximum file size: {maxSize}MB
                      {multiple && ` (up to ${maxFiles} files)`}
                    </div>
                    
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileUpload}
                      multiple={multiple}
                      accept={allowedTypes.join(',')}
                      style={{ display: 'none' }}
                    />
                  </div>

                  {/* Selected Files Preview */}
                  {selectedMedia.length > 0 && (
                    <div style={{
                      marginTop: '24px'
                    }}>
                      <h4 style={{
                        fontSize: '16px',
                        fontWeight: 'bold',
                        color: 'white',
                        marginBottom: '16px'
                      }}>
                        Selected Files ({selectedMedia.length})
                      </h4>
                      
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
                        gap: '16px'
                      }}>
                        {selectedMedia.map(media => (
                          <div
                            key={media.id}
                            style={{
                              borderRadius: '4px',
                              overflow: 'hidden',
                              border: '2px solid #4945ff',
                              backgroundColor: '#181826',
                              position: 'relative'
                            }}
                          >
                            <div style={{
                              width: '100%',
                              paddingTop: '75%', // 4:3 aspect ratio
                              position: 'relative',
                              backgroundColor: '#32324d'
                            }}>
                              <img
                                src={media.url}
                                alt={media.name}
                                style={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover'
                                }}
                              />
                              
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemove(media.id);
                                }}
                                style={{
                                  position: 'absolute',
                                  top: '8px',
                                  right: '8px',
                                  width: '24px',
                                  height: '24px',
                                  borderRadius: '50%',
                                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                  border: 'none',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: 'white',
                                  fontSize: '14px',
                                  cursor: 'pointer'
                                }}
                              >
                                ✕
                              </button>
                            </div>
                            
                            <div style={{
                              padding: '8px',
                              borderTop: '1px solid #32324d'
                            }}>
                              <div style={{
                                fontSize: '12px',
                                fontWeight: '500',
                                color: 'white',
                                marginBottom: '4px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                              }}>
                                {media.name}
                              </div>
                              <div style={{
                                fontSize: '10px',
                                color: '#a5a5ba',
                                display: 'flex',
                                justifyContent: 'space-between'
                              }}>
                                <span>{formatFileSize(media.size)}</span>
                                <span>{media.mime.split('/')[1]}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Modal Footer */}
            <div style={{
              padding: '16px 24px',
              borderTop: '1px solid #32324d',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div style={{
                fontSize: '14px',
                color: '#a5a5ba'
              }}>
                {multiple ? (
                  `${selectedMedia.length} of ${maxFiles} files selected`
                ) : (
                  selectedMedia.length > 0 ? '1 file selected' : 'No file selected'
                )}
              </div>
              
              <div style={{
                display: 'flex',
                gap: '12px'
              }}>
                <button
                  onClick={handleCancel}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: 'transparent',
                    color: '#a5a5ba',
                    border: '1px solid #32324d',
                    borderRadius: '4px',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#32324d';
                    e.currentTarget.style.color = 'white';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#a5a5ba';
                  }}
                >
                  Cancel
                </button>
                
                <button
                  onClick={handleApply}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#4945ff',
                    color: 'white',
                    border: '1px solid #4945ff',
                    borderRadius: '4px',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#3832e1';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#4945ff';
                  }}
                >
                  Apply
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaPicker;