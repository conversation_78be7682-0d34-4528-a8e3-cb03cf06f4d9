import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface BaseAddDialogProps {
  title: string;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  children: React.ReactNode;
  showPublishedSwitch?: boolean;
  initialPublished?: boolean;
  onPublishedChange?: (published: boolean) => void;
}

const BaseAddDialog = ({
  title,
  onSubmit,
  onCancel,
  isSubmitting = false,
  children,
  showPublishedSwitch = false,
  initialPublished = false,
  onPublishedChange
}: BaseAddDialogProps) => {
  const [isPublished, setIsPublished] = useState(initialPublished);

  const handlePublishedToggle = () => {
    const newValue = !isPublished;
    setIsPublished(newValue);
    onPublishedChange?.(newValue);
  };

  return (
    <div style={{
      padding: '32px',
      backgroundColor: '#181826',
      minHeight: '100vh'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '32px'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          }}>
            <button
              onClick={onCancel}
              style={{
                padding: '8px',
                backgroundColor: 'transparent',
                border: '1px solid #32324d',
                borderRadius: '4px',
                color: '#a5a5ba',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              title="Back"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M19 12H5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 19L5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            
            <h1 style={{
              fontSize: '28px',
              fontWeight: 'bold',
              color: 'white',
              margin: 0
            }}>
              {title}
            </h1>
          </div>
        </div>

        {/* Form Container */}
        <div style={{
          backgroundColor: '#212134',
          borderRadius: '8px',
          padding: '32px',
          border: '1px solid #32324d'
        }}>
          {/* Published Switch */}
          {showPublishedSwitch && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              marginBottom: '24px',
              paddingBottom: '24px',
              borderBottom: '1px solid #32324d'
            }}>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                cursor: 'pointer',
                color: 'white',
                fontSize: '16px'
              }}>
                <div style={{
                  position: 'relative',
                  width: '48px',
                  height: '24px',
                  backgroundColor: isPublished ? '#4945ff' : '#32324d',
                  borderRadius: '12px',
                  transition: 'background-color 0.2s',
                  cursor: 'pointer'
                }} onClick={handlePublishedToggle}>
                  <div style={{
                    position: 'absolute',
                    top: '2px',
                    left: isPublished ? '26px' : '2px',
                    width: '20px',
                    height: '20px',
                    backgroundColor: 'white',
                    borderRadius: '50%',
                    transition: 'left 0.2s',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                  }} />
                </div>
                <span>Published</span>
              </label>
            </div>
          )}

          {/* Form Content */}
          {children}
        </div>
      </div>

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default BaseAddDialog;