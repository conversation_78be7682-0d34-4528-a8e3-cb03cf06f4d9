import { useState, useRef, useEffect } from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
}

const Pagination = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange
}: PaginationProps) => {
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Available options for items per page
  const itemsPerPageOptions = [10, 20, 50, 100, 200, 500];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle page number input
  const [pageInput, setPageInput] = useState(currentPage.toString());

  useEffect(() => {
    setPageInput(currentPage.toString());
  }, [currentPage]);

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInput(e.target.value);
  };

  const handlePageInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const page = parseInt(pageInput);
      if (!isNaN(page) && page >= 1 && page <= totalPages) {
        onPageChange(page);
      } else {
        setPageInput(currentPage.toString());
      }
    }
  };

  const handlePageInputBlur = () => {
    const page = parseInt(pageInput);
    if (!isNaN(page) && page >= 1 && page <= totalPages) {
      onPageChange(page);
    } else {
      setPageInput(currentPage.toString());
    }
  };

  return (
    <div style={{
      padding: '16px',
      backgroundColor: '#212134',
      borderRadius: '4px',
      boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <span style={{
          fontSize: '14px',
          color: '#a5a5ba'
        }}>
          Showing {totalItems > 0 ? `${startItem}-${endItem} of ${totalItems}` : '0'} items
        </span>

        <div style={{
          display: 'flex',
          gap: '12px',
          alignItems: 'center'
        }}>
          {/* Items per page dropdown */}
          <div style={{ position: 'relative' }} ref={dropdownRef}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <span style={{
                fontSize: '14px',
                color: '#a5a5ba'
              }}>
                Entries per page:
              </span>

              <div
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                style={{
                  padding: '6px 12px',
                  backgroundColor: '#181826',
                  color: 'white',
                  border: '1px solid #32324d',
                  borderRadius: '4px',
                  fontSize: '14px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  minWidth: '80px',
                  justifyContent: 'space-between'
                }}
              >
                <span>{itemsPerPage}</span>
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  style={{
                    transform: isDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease'
                  }}
                >
                  <path
                    d="M6 9L12 15L18 9"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>

            {isDropdownOpen && (
              <div style={{
                position: 'absolute',
                top: '100%',
                left: 0,
                marginTop: '4px',
                backgroundColor: '#212134',
                border: '1px solid #32324d',
                borderRadius: '4px',
                zIndex: 1000,
                width: '100%',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
              }}>
                {itemsPerPageOptions.map(option => (
                  <div
                    key={option}
                    onClick={() => {
                      onItemsPerPageChange(option);
                      setIsDropdownOpen(false);
                    }}
                    style={{
                      padding: '8px 12px',
                      cursor: 'pointer',
                      backgroundColor: option === itemsPerPage ? '#4945ff' : 'transparent',
                      color: option === itemsPerPage ? 'white' : '#a5a5ba',
                      fontSize: '14px'
                    }}
                    onMouseEnter={(e) => {
                      if (option !== itemsPerPage) {
                        e.currentTarget.style.backgroundColor = '#32324d';
                        e.currentTarget.style.color = 'white';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (option !== itemsPerPage) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#a5a5ba';
                      }
                    }}
                  >
                    {option}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pagination controls */}
          <div style={{
            display: 'flex',
            gap: '8px',
            alignItems: 'center'
          }}>
            <button
              onClick={() => onPageChange(1)}
              disabled={currentPage === 1}
              style={{
                padding: '6px 10px',
                backgroundColor: currentPage === 1 ? '#32324d' : '#181826',
                color: currentPage === 1 ? '#a5a5ba' : 'white',
                border: '1px solid #32324d',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                outline: 'none'
              }}
              title="First page"
            >
              «
            </button>

            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              style={{
                padding: '6px 10px',
                backgroundColor: currentPage === 1 ? '#32324d' : '#181826',
                color: currentPage === 1 ? '#a5a5ba' : 'white',
                border: '1px solid #32324d',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                outline: 'none'
              }}
              title="Previous page"
            >
              ‹
            </button>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}>
              <span style={{
                fontSize: '14px',
                color: '#a5a5ba'
              }}>
                Page
              </span>

              <input
                type="text"
                value={pageInput}
                onChange={handlePageInputChange}
                onKeyDown={handlePageInputKeyDown}
                onBlur={handlePageInputBlur}
                style={{
                  width: '40px',
                  padding: '4px 8px',
                  backgroundColor: '#181826',
                  color: 'white',
                  border: '1px solid #32324d',
                  borderRadius: '4px',
                  fontSize: '14px',
                  textAlign: 'center'
                }}
                title="Go to page"
              />

              <span style={{
                fontSize: '14px',
                color: '#a5a5ba'
              }}>
                of {totalPages}
              </span>
            </div>

            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages || totalPages === 0}
              style={{
                padding: '6px 10px',
                backgroundColor: currentPage === totalPages || totalPages === 0 ? '#32324d' : '#181826',
                color: currentPage === totalPages || totalPages === 0 ? '#a5a5ba' : 'white',
                border: '1px solid #32324d',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: currentPage === totalPages || totalPages === 0 ? 'not-allowed' : 'pointer',
                outline: 'none'
              }}
              title="Next page"
            >
              ›
            </button>

            <button
              onClick={() => onPageChange(totalPages)}
              disabled={currentPage === totalPages || totalPages === 0}
              style={{
                padding: '6px 10px',
                backgroundColor: currentPage === totalPages || totalPages === 0 ? '#32324d' : '#181826',
                color: currentPage === totalPages || totalPages === 0 ? '#a5a5ba' : 'white',
                border: '1px solid #32324d',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: currentPage === totalPages || totalPages === 0 ? 'not-allowed' : 'pointer',
                outline: 'none'
              }}
              title="Last page"
            >
              »
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pagination;
