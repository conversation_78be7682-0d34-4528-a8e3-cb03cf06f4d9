import { Box, Typography, Grid, Card, CardBody, Flex, Button } from '@strapi/design-system';
import {
  Image,
  ShoppingCart,
  User,
  Stack,
  Folder,
  ChartBubble,
  ArrowRight
} from '@strapi/icons';
// import MetricsTable from './MetricsTable';

interface DashboardOverviewProps {
  title: string;
  onCollectionSelect: (collection: string) => void;
}

const quickActions = [
  {
    id: 'banners',
    name: 'Banners',
    icon: Image,
    description: 'Manage promotional banners',
    color: 'primary'
  },
  {
    id: 'products',
    name: 'Products',
    icon: ShoppingCart,
    description: 'Manage your product catalog',
    color: 'success'
  },
  {
    id: 'vendors',
    name: 'Vendors',
    icon: User,
    description: 'Manage vendor accounts',
    color: 'warning'
  },
  {
    id: 'categories',
    name: 'Categories',
    icon: Stack,
    description: 'Organize product categories',
    color: 'secondary'
  },
];

const DashboardOverview = ({ title, onCollectionSelect }: DashboardOverviewProps) => {
  return (
    <Box padding={8}>
      {/* Header */}
      <Box marginBottom={8}>
        <Typography variant="alpha" marginBottom={2}>
          Welcome to {title}
        </Typography>
        <Typography variant="omega" textColor="neutral600">
          Manage your content with our enhanced dashboard experience. Professional CRUD operations with modern UI.
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Box marginBottom={8}>
        <Typography variant="beta" marginBottom={4}>
          Quick Stats
        </Typography>
        <Grid gap={4} gridCols={4}>
          <Card>
            <CardBody>
              <Flex direction="column" alignItems="center" gap={2}>
                <Typography variant="alpha" textColor="primary600">
                  0
                </Typography>
                <Typography variant="omega" textColor="neutral600">
                  Total Banners
                </Typography>
              </Flex>
            </CardBody>
          </Card>
          <Card>
            <CardBody>
              <Flex direction="column" alignItems="center" gap={2}>
                <Typography variant="alpha" textColor="success600">
                  0
                </Typography>
                <Typography variant="omega" textColor="neutral600">
                  Total Products
                </Typography>
              </Flex>
            </CardBody>
          </Card>
          <Card>
            <CardBody>
              <Flex direction="column" alignItems="center" gap={2}>
                <Typography variant="alpha" textColor="warning600">
                  0
                </Typography>
                <Typography variant="omega" textColor="neutral600">
                  Total Vendors
                </Typography>
              </Flex>
            </CardBody>
          </Card>
          <Card>
            <CardBody>
              <Flex direction="column" alignItems="center" gap={2}>
                <Typography variant="alpha" textColor="secondary600">
                  0
                </Typography>
                <Typography variant="omega" textColor="neutral600">
                  Total Categories
                </Typography>
              </Flex>
            </CardBody>
          </Card>
        </Grid>
      </Box>

      {/* Quick Actions */}
      <Box marginBottom={8}>
        <Typography variant="beta" marginBottom={4}>
          Quick Actions
        </Typography>
        <Grid gap={4}>
          {quickActions.map((action) => {
            const IconComponent = action.icon;
            return (
              <Card key={action.id} style={{ cursor: 'pointer' }}>
                <CardBody>
                  <Flex direction="column" alignItems="flex-start" gap={3}>
                    <Flex alignItems="center" gap={2}>
                      <Box
                        background={`${action.color}100`}
                        padding={2}
                        borderRadius="4px"
                      >
                        <IconComponent />
                      </Box>
                      <Typography variant="delta" fontWeight="bold">
                        {action.name}
                      </Typography>
                    </Flex>
                    <Typography variant="omega" textColor="neutral600">
                      {action.description}
                    </Typography>
                    <Button
                      variant="tertiary"
                      size="S"
                      endIcon={<ArrowRight />}
                      onClick={() => onCollectionSelect(action.id)}
                    >
                      Manage
                    </Button>
                  </Flex>
                </CardBody>
              </Card>
            );
          })}
        </Grid>
      </Box>

      {/* Metrics Section */}
      <Box>
        <Typography variant="beta" marginBottom={4}>
          Content Overview
        </Typography>
        <Card>
          <CardBody>
            <Typography variant="omega" textColor="neutral600">
              Content metrics will be displayed here
            </Typography>
          </CardBody>
        </Card>
      </Box>
    </Box>
  );
};

export default DashboardOverview;
