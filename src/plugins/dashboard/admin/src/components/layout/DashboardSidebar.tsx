import { Box, Typo<PERSON>, Button, Divider, Flex } from '@strapi/design-system';
import { 
  Image, 
  ShoppingCart, 
  User, 
  Stack, 
  Folder,
  More,
  ChartBubble
} from '@strapi/icons';

interface Collection {
  id: string;
  name: string;
  icon: React.ComponentType;
  count?: number;
}

interface DashboardSidebarProps {
  selectedCollection: string | null;
  onCollectionSelect: (collection: string) => void;
}

const collections: Collection[] = [
  { id: 'banners', name: 'Banners', icon: Image },
  { id: 'products', name: 'Products', icon: ShoppingCart },
  { id: 'vendors', name: 'Vendors', icon: User },
  { id: 'categories', name: 'Categories', icon: Stack },
  { id: 'main-categories', name: 'Main Categories', icon: Folder },
  { id: 'orders', name: 'Orders', icon: ChartBubble },
];

const DashboardSidebar = ({ selectedCollection, onCollectionSelect }: DashboardSidebarProps) => {
  return (
    <Box 
      width="280px" 
      background="neutral0" 
      borderRight="1px solid" 
      borderColor="neutral200"
      height="100vh"
      padding={4}
    >
      {/* Header */}
      <Flex alignItems="center" gap={2} marginBottom={6}>
        <More />
        <Typography variant="beta" fontWeight="bold">
          Custom Dashboard
        </Typography>
      </Flex>

      <Divider marginBottom={4} />

      {/* Overview Button */}
      <Button
        variant={!selectedCollection ? 'default' : 'tertiary'}
        fullWidth
        justifyContent="flex-start"
        onClick={() => onCollectionSelect('')}
        marginBottom={2}
      >
        <Flex alignItems="center" gap={2}>
          <More />
          <Typography variant="omega">Overview</Typography>
        </Flex>
      </Button>

      <Divider marginY={4} />

      {/* Collections */}
      <Typography variant="sigma" textColor="neutral600" marginBottom={3}>
        COLLECTIONS
      </Typography>

      {collections.map((collection) => {
        const IconComponent = collection.icon;
        const isSelected = selectedCollection === collection.id;
        
        return (
          <Button
            key={collection.id}
            variant={isSelected ? 'default' : 'tertiary'}
            fullWidth
            justifyContent="flex-start"
            onClick={() => onCollectionSelect(collection.id)}
            marginBottom={1}
          >
            <Flex alignItems="center" gap={2} width="100%">
              <IconComponent />
              <Typography variant="omega" flex="1" textAlign="left">
                {collection.name}
              </Typography>
              {collection.count && (
                <Box 
                  background="primary100" 
                  paddingX={2} 
                  paddingY={1} 
                  borderRadius="4px"
                >
                  <Typography variant="pi" textColor="primary600">
                    {collection.count}
                  </Typography>
                </Box>
              )}
            </Flex>
          </Button>
        );
      })}
    </Box>
  );
};

export default DashboardSidebar;
