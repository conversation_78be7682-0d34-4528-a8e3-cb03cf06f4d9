import React, { useState } from 'react';
import { Main, Box, Flex } from '@strapi/design-system';

import DashboardSidebar from '../components/DashboardSidebar';
import DashboardContent from '../components/DashboardContent';
import DashboardOverview from '../components/DashboardOverview';

const HomePage = () => {
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);

  return (
    <Main>
      <Flex height="100vh">
        <DashboardSidebar
          selectedCollection={selectedCollection}
          onCollectionSelect={setSelectedCollection}
        />
        <Box flex="1" background="neutral100">
          {selectedCollection ? (
            <DashboardContent
              collection={selectedCollection}
              onBack={() => setSelectedCollection(null)}
            />
          ) : (
            <DashboardOverview
              title="Custom Dashboard"
              onCollectionSelect={setSelectedCollection}
            />
          )}
        </Box>
      </Flex>
    </Main>
  );
};

export { HomePage };
