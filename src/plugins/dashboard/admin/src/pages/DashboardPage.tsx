import { useState, useEffect } from 'react';

import { getTranslation } from '../utils/getTranslation';
import { useSafeIntl, useSafeRouter } from '../utils/contextSafety';
import FilterBar from '../components/shared/FilterBar';
import SideMenu from '../components/shared/SideMenu';
import BannerDataTable from '../components/shared/BannerDataTable';

const DashboardPage = () => {
  const { formatMessage } = useSafeIntl();
  const { searchParams: [searchParams, setSearchParams], location } = useSafeRouter();

  // Get initial collection from URL params or default to overview
  const getInitialCollection = () => {
    const collectionParam = searchParams.get('collection');
    if (collectionParam) return collectionParam;

    // Check if we're on the banners route
    if (location.pathname.includes('/banners')) return 'banners';

    return 'overview';
  };

  const [selectedCollection, setSelectedCollection] = useState(getInitialCollection());
  const [filters, setFilters] = useState({
    search: '',
    vendor: 'all',
    dateRange: null
  });
  const [advancedFilters, setAdvancedFilters] = useState({
    dateFrom: '',
    dateTo: '',
    status: 'published'
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Update collection when URL changes
  useEffect(() => {
    const newCollection = getInitialCollection();
    setSelectedCollection(newCollection);
  }, [searchParams, location.pathname]);

  const handleCollectionChange = (collection: string) => {
    setSelectedCollection(collection);
    // Update URL params to maintain state
    if (collection === 'overview') {
      setSearchParams({});
    } else {
      setSearchParams({ collection });
    }
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return (
    <main style={{
      padding: '32px',
      backgroundColor: '#181826',
      minHeight: '100vh'
    }}>
      {/* Main Content Layout */}
      <div style={{
        display: 'flex',
        gap: '24px'
      }}>
        {/* Side Menu */}
        <div style={{
          width: '300px',
          flexShrink: 0
        }}>
          <SideMenu
            selectedCollection={selectedCollection}
            onCollectionChange={handleCollectionChange}
          />
        </div>

        {/* Content Column */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }}>
          {/* Filter Bar */}
          <div>
            <FilterBar
              filters={filters}
              onFiltersChange={handleFiltersChange}
              selectedCollection={selectedCollection}
              advancedFilters={advancedFilters}
              onAdvancedFiltersChange={setAdvancedFilters}
              showAdvancedFilters={showAdvancedFilters}
              onAdvancedFiltersToggle={setShowAdvancedFilters}
            />
          </div>

          {/* Content Area */}
          <div style={{ flex: 1 }}>
            <div style={{
              backgroundColor: '#212134',
              padding: '24px',
              borderRadius: '4px',
              boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)',
              minHeight: '600px'
            }}>
            {selectedCollection === 'overview' ? (
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  color: 'white',
                  margin: '0 0 16px 0'
                }}>
                  {formatMessage({
                    id: getTranslation('dashboard.overview.title'),
                    defaultMessage: 'Overview'
                  })}
                </h2>
                <p style={{
                  fontSize: '14px',
                  color: '#a5a5ba',
                  margin: 0
                }}>
                  {formatMessage({
                    id: getTranslation('dashboard.overview.description'),
                    defaultMessage: 'Select a collection from the sidebar to manage your content.'
                  })}
                </p>
              </div>
            ) : selectedCollection === 'banners' ? (
              <div>
                <BannerDataTable
                  searchTerm={filters.search}
                  vendorFilter={filters.vendor}
                  advancedFilters={advancedFilters}
                />
              </div>
            ) : (
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  color: 'white',
                  margin: '0 0 16px 0'
                }}>
                  {selectedCollection.charAt(0).toUpperCase() + selectedCollection.slice(1)} Collection
                </h2>
                <p style={{
                  fontSize: '14px',
                  color: '#a5a5ba',
                  marginBottom: '24px'
                }}>
                  Manage your {selectedCollection} data with full CRUD operations, search, and filtering.
                </p>

                {/* Data Table will be implemented for other collections */}
                <div style={{
                  backgroundColor: '#181826',
                  padding: '20px',
                  borderRadius: '4px',
                  border: '1px solid #32324d'
                }}>
                  <p style={{
                    color: '#a5a5ba',
                    textAlign: 'center',
                    margin: 0
                  }}>
                    📊 Data table for {selectedCollection} will be implemented next
                  </p>
                </div>
              </div>
            )}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export { DashboardPage };
