import React, { Suspense } from 'react';
import { Page } from '@strapi/strapi/admin';
import { Routes, Route } from 'react-router-dom';

import { DashboardPage } from './DashboardPage';
import BannerAddPage from '../components/BannerAddPage';
import NotFoundPage from '../components/NotFoundPage';

// Loading component
const LoadingComponent = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    backgroundColor: '#181826',
    color: 'white'
  }}>
    <div>Loading...</div>
  </div>
);

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Plugin Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: '#181826',
          color: 'white',
          flexDirection: 'column',
          gap: '16px'
        }}>
          <div>Something went wrong with the dashboard plugin.</div>
          <button
            onClick={() => this.setState({ hasError: false })}
            style={{
              padding: '8px 16px',
              backgroundColor: '#4945ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

const App = () => {
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingComponent />}>
        <Routes>
          <Route index element={<DashboardPage />} />
          <Route path="banners" element={<DashboardPage />} />
          <Route path="banners/add" element={<BannerAddPage />} />
          <Route path="banners/edit/:id" element={<BannerAddPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

export { App };
