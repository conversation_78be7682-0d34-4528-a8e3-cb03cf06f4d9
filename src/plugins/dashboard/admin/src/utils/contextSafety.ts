import React from 'react';
import { useIntl } from 'react-intl';
import { useSearchParams, useLocation } from 'react-router-dom';

/**
 * Safe wrapper for useIntl hook to handle context initialization issues
 */
export const useSafeIntl = () => {
  try {
    return useIntl();
  } catch (error) {
    console.warn('useIntl context not available, using fallback');
    return {
      formatMessage: ({ defaultMessage }: { defaultMessage: string }) => defaultMessage
    };
  }
};

/**
 * Safe wrapper for router hooks to handle context initialization issues
 */
export const useSafeRouter = () => {
  try {
    const searchParams = useSearchParams();
    const location = useLocation();
    return { searchParams, location };
  } catch (error) {
    console.warn('Router context not available, using fallback');
    return {
      searchParams: [new URLSearchParams(), () => {}] as const,
      location: { pathname: '/' }
    };
  }
};

/**
 * Higher-order component to wrap components with context safety
 */
export const withContextSafety = <P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> => {
  return (props: P) => {
    try {
      return React.createElement(Component, props);
    } catch (error) {
      console.error('Context error in component:', error);
      return React.createElement('div', {
        style: {
          padding: '20px',
          backgroundColor: '#212134',
          color: 'white',
          borderRadius: '4px',
          textAlign: 'center' as const
        }
      }, [
        React.createElement('p', { key: 'text' }, 'Loading component...'),
        React.createElement('button', {
          key: 'button',
          onClick: () => window.location.reload(),
          style: {
            marginTop: '10px',
            padding: '8px 16px',
            backgroundColor: '#4945ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }
        }, 'Refresh')
      ]);
    }
  };
};