{"kind": "collectionType", "collectionName": "vendors", "info": {"singularName": "vendor", "pluralName": "vendors", "displayName": "Vend<PERSON>", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "business_name": {"type": "string", "unique": true}, "email": {"type": "string"}, "address": {"type": "text"}, "business_type": {"type": "enumeration", "default": "<PERSON><PERSON><PERSON>", "enum": ["<PERSON><PERSON><PERSON>", "Accessories", "Gifts", "Market", "Medical", "Shoes", "Restaurant", "<PERSON><PERSON><PERSON>", "Others"]}, "active": {"type": "boolean", "default": true}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "password": {"type": "string"}, "device_token": {"type": "string"}, "phone": {"type": "string"}, "website_link": {"type": "string"}, "app_store_link": {"type": "string"}, "play_store_link": {"type": "string"}, "type": {"type": "enumeration", "enum": ["free", "monthly", "quarter", "semester", "annually", "deleted"]}, "pricing": {"type": "relation", "relation": "manyToOne", "target": "api::price.price", "inversedBy": "vendors"}, "expire_date": {"type": "date"}, "categories": {"type": "relation", "relation": "oneToMany", "target": "api::product-category.product-category", "private": true, "mappedBy": "vendor"}, "banners": {"type": "relation", "relation": "oneToMany", "target": "api::banner.banner", "private": true, "mappedBy": "vendor"}, "payments": {"type": "relation", "relation": "oneToMany", "target": "api::payment.payment", "private": true, "mappedBy": "vendor"}, "users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "private": true, "mappedBy": "vendor"}, "expenses": {"type": "relation", "relation": "oneToMany", "target": "api::expense.expense", "private": true, "mappedBy": "vendor"}, "tasks": {"type": "relation", "relation": "oneToMany", "target": "api::task.task", "private": true, "mappedBy": "vendor"}, "shippings": {"type": "relation", "relation": "oneToMany", "target": "api::shipping.shipping", "private": true, "mappedBy": "vendor"}, "client": {"type": "relation", "relation": "oneToOne", "target": "api::client.client", "private": true, "mappedBy": "vendor"}, "notifications": {"type": "relation", "relation": "oneToMany", "target": "api::notification.notification", "private": true, "mappedBy": "vendor"}, "orders": {"type": "relation", "relation": "oneToMany", "target": "api::order.order", "private": true, "mappedBy": "vendor"}, "products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product", "private": true, "mappedBy": "vendor"}, "config": {"type": "relation", "relation": "oneToOne", "target": "api::config.config", "mappedBy": "vendor"}, "device_type": {"type": "enumeration", "enum": ["Android", "iOS", "Web"]}, "paid_amount": {"type": "decimal"}, "start_date": {"type": "date"}, "about": {"type": "component", "component": "about.about", "repeatable": false}, "promo_codes": {"type": "relation", "relation": "oneToMany", "target": "api::promo-code.promo-code", "private": true, "mappedBy": "vendor"}, "qr_landing": {"type": "component", "component": "qr-landing.landing", "repeatable": false}, "main_categories": {"type": "relation", "relation": "oneToMany", "target": "api::main-category.main-category", "mappedBy": "vendor"}, "vendor_webview_features": {"type": "component", "component": "vendor-webview-feature.vendor-webview-feature", "repeatable": true}, "global_sale": {"type": "decimal"}, "is_global_sale_percentage": {"type": "boolean"}, "roles": {"type": "component", "component": "permissions.vendor-permissions", "repeatable": true}, "parent_vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "sub_vendors"}, "sub_vendors": {"type": "relation", "relation": "oneToMany", "target": "api::vendor.vendor", "mappedBy": "parent_vendor"}}}