# Dashboard Plugin Context Fix

## Problem
The custom dashboard plugin was experiencing a "Cannot read properties of null (reading 'useContext')" error when clicking the dashboard icon. This error occurred intermittently and was resolved by refreshing the page, indicating a React context initialization timing issue.

## Root Cause
The error was caused by React hooks (specifically `useIntl` and router hooks) being called before the React context providers were fully initialized. This is a common issue in Strapi v5 plugins where components can be rendered before all necessary contexts are available.

## Solution Implemented

### 1. Error Boundary and Suspense Wrapper
- Added an `ErrorBoundary` component to catch and handle context-related errors gracefully
- Wrapped the main App component with `Suspense` to handle async loading
- Added a loading component to show while contexts are initializing

### 2. Context Safety Utilities
Created `/src/plugins/dashboard/admin/src/utils/contextSafety.ts` with:
- `useSafeIntl()`: Safe wrapper for `useIntl` hook with fallback
- `useSafeRouter()`: Safe wrapper for router hooks with fallback
- `withContextSafety()`: Higher-order component for additional safety

### 3. Component Updates
Updated all components to use safe context hooks:
- `DashboardPage.tsx`
- `SideMenu.tsx`
- `FilterBar.tsx`
- `BannerDataTable.tsx`
- `VendorDropdown.tsx`

### 4. Plugin Registration Enhancement
- Added a small delay (100ms) in the plugin component loading to ensure contexts are ready
- This prevents the race condition between plugin loading and context initialization

## Files Modified

### Context Safety Fixes:
1. **App.tsx**: Added ErrorBoundary and Suspense wrappers
2. **contextSafety.ts**: New utility file with safe context hooks
3. **DashboardPage.tsx**: Updated to use safe context hooks
4. **SideMenu.tsx**: Updated to use safe context hooks
5. **FilterBar.tsx**: Updated to use safe context hooks
6. **BannerDataTable.tsx**: Updated to use safe context hooks
7. **VendorDropdown.tsx**: Updated to use safe context hooks
8. **index.ts**: Added delay in component loading

### UI/UX Improvements:
9. **BannerDataTable.tsx**: Fixed ID column display (now shows ID + checkbox)
10. **BaseAddDialog.tsx**: New reusable dialog component with integrated publish switch
11. **SimpleMediaPicker.tsx**: New media picker component with better UI
12. **BannerAddPage.tsx**: Completely redesigned with new components and better UX

## How to Test

1. **Build the plugin**:
   ```bash
   cd src/plugins/dashboard
   npm run build
   ```

2. **Build the main application**:
   ```bash
   cd ../../../
   npm run build
   ```

3. **Start the development server**:
   ```bash
   npm run develop
   ```

4. **Test the dashboard**:
   - Navigate to the Strapi admin panel
   - Click on the Dashboard plugin icon multiple times
   - The error should no longer occur
   - If any context issues arise, the error boundary will show a user-friendly message with a "Try Again" button

## Benefits

### Context Safety:
1. **Eliminates Context Errors**: Prevents the "useContext" null reference errors
2. **Graceful Error Handling**: Shows user-friendly error messages instead of breaking the UI
3. **Better User Experience**: No more need to refresh the page when errors occur
4. **Maintainable Code**: Centralized context safety utilities can be reused across components
5. **Future-Proof**: Handles potential context timing issues in future Strapi updates

### UI/UX Improvements:
6. **Better Data Visibility**: ID column now shows both checkbox and actual ID values
7. **Improved Media Management**: Visual media picker with preview and library browsing
8. **Streamlined Publishing**: Integrated publish/draft switch in add dialogs
9. **Reusable Components**: BaseAddDialog can be used for other content types
10. **Enhanced User Experience**: More intuitive and visually appealing interface

## Technical Details

The fix addresses the fundamental issue where React hooks were being called before the React context tree was fully established. By implementing safe wrappers and error boundaries, we ensure that:

- Components can render even if contexts aren't immediately available
- Fallback values are provided when contexts are missing
- Users get clear feedback when something goes wrong
- The application remains stable and usable

This solution is compatible with Strapi v5.20.0 and should work with future versions as well.